import random
import time
import contextlib
import io
import threading
import os
import importlib.util  # For dynamic loading
import sys             # For dynamic loading
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing
import math  # For statistical calculations

# --- Configuration ---
BOARD_WIDTH = 11
BOARD_HEIGHT = 11
TURN_DELAY_SECONDS = 0 # Set to 0 for instant simulation, or >0 for pauses

# --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- ---
# --- DEFINE YOUR BATTLESNAKE AI FILES HERE ---
# --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- ---
SNAKE_FILES_TO_LOAD = [
    "wurmchen.py",  # Make sure this file exists!
    "wurmchen.py",
]
# --- >>> --- >>> --- >>> --- >>> --- >>> --- >>> --- >>> --- >>> --- ---

# --- AI Loading Function ---
ai_load_counter = 0
ai_load_lock = threading.Lock()

def load_ai_from_file(filepath, heuristic_values=None, silent=False):
    global ai_load_counter
    if not os.path.exists(filepath):
        if not silent: print(f"❌ Error: File not found - {filepath}")
        return None

    # Thread-safe counter increment
    with ai_load_lock:
        base_name = os.path.splitext(os.path.basename(filepath))[0]
        module_name = f"{base_name}_{ai_load_counter}"
        ai_load_counter += 1
    # Thread-safe module loading
    with ai_load_lock:
        spec = importlib.util.spec_from_file_location(module_name, filepath)
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            sys.modules[module_name] = module
            try:
                # Suppress prints during loading
                with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
                    spec.loader.exec_module(module)
                if not silent: print(f"✅ Successfully loaded AI from {filepath} as {module_name}")
                if hasattr(module, 'info') and hasattr(module, 'move'):
                    if not hasattr(module, 'start'): module.start = lambda gs: None
                    if not hasattr(module, 'end'): module.end = lambda gs: None

                    # If heuristic values are provided, override the module's heuristic values
                    if heuristic_values is not None and hasattr(module, 'heuristikValues'):
                        module.heuristikValues = heuristic_values.copy()

                    # Set silent mode if available
                    if hasattr(module, 'silent_mode'):
                        module.silent_mode = silent

                    return module
                else:
                    if not silent: print(f"❌ Error: {filepath} missing 'info' or 'move'.")
                    return None
            except Exception as e:
                if not silent: print(f"❌ Error executing {filepath}: {e}")
                if module_name in sys.modules: del sys.modules[module_name]
                return None
        else:
            if not silent: print(f"❌ Error: Could not load {filepath}")
            return None

# --- Simulation Core Classes (Point, Snake) ---
class Point:
    def __init__(self, x, y): self.x, self.y = x, y
    def __eq__(self, o): return isinstance(o, Point) and self.x == o.x and self.y == o.y
    def __hash__(self): return hash((self.x, self.y))
    def __repr__(self): return f"P({self.x},{self.y})"
    def to_dict(self): return {"x": self.x, "y": self.y}
    def moved(self, m):
        if m=="up": return Point(self.x, self.y+1)
        if m=="down": return Point(self.x, self.y-1)
        if m=="left": return Point(self.x-1, self.y)
        if m=="right": return Point(self.x+1, self.y)
        return self

class Snake:
    def __init__(self, id, name, body_points, ai_module):
        self.id, self.name, self.body = id, name, list(body_points)
        self.health, self.ai, self.is_alive = 100, ai_module, True
        self.last_move_made = "up"
        try: self.info_data = self.ai.info()
        except Exception: self.info_data = {"author": name}

    @property
    def head(self): return self.body[0]
    def to_dict(self): return {"id":self.id, "name":self.name, "health":self.health, "body":[p.to_dict() for p in self.body], "head":self.head.to_dict(), "length":len(self.body), "latency":"0", "shout":""}

# --- Simulation Class ---
class BattlesnakeSimulation:
    def __init__(self, width, height, snake_ais, silent=False):
        self.width, self.height, self.snake_ais = width, height, snake_ais
        self.turn, self.snakes, self.food = 0, [], []
        self.game_id = f"sim-{random.randint(1000, 9999)}"
        self.silent = silent
        self._setup_game()

    def _setup_game(self):
        start_positions = self._get_start_positions(len(self.snake_ais))
        for i, ai_module in enumerate(self.snake_ais):
            info = {}
            # Suppress info() prints
            with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
                try: info = ai_module.info()
                except Exception as e: print(f"⚠️ Warning: info() for AI {i}: {e}")
            author = info.get('author', f'Unknown_{i}')
            snake_id = f"{author}-{i}"
            start_pos = start_positions[i]
            snake = Snake(snake_id, author, [start_pos]*3, ai_module)
            self.snakes.append(snake)
            gs = self._build_game_state(snake.id)
            # Suppress start() prints
            with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
                try: snake.ai.start(gs)
                except Exception as e: print(f"Error calling start for {snake.name}: {e}")
        self._spawn_food(count=min(len(self.snakes) + 1, 5))

    def _get_start_positions(self, num_snakes):
        w, h = self.width - 1, self.height - 1; margin = 2
        corners = [Point(margin, margin), Point(w-margin, h-margin), Point(margin, h-margin), Point(w-margin, margin)]
        mid_points = [Point(w//2, margin), Point(w//2, h-margin), Point(margin, h//2), Point(w-margin, h//2)]
        all_pos = corners + mid_points; random.shuffle(all_pos)
        return all_pos[:num_snakes] if num_snakes <= len(all_pos) else [Point(random.randint(1,w-1), random.randint(1,h-1)) for _ in range(num_snakes)]

    def _spawn_food(self, count=1):
        occupied = {p for s in self.snakes if s.is_alive for p in s.body} | set(self.food)
        for _ in range(count):
            for _ in range(100):
                x, y = random.randint(0, self.width-1), random.randint(0, self.height-1)
                nf = Point(x, y)
                if nf not in occupied: self.food.append(nf); occupied.add(nf); break

    def _build_game_state(self, current_snake_id):
        return {"game":{"id":self.game_id}, "turn":self.turn, "board":{"height":self.height, "width":self.width, "snakes":[s.to_dict() for s in self.snakes if s.is_alive], "food":[f.to_dict() for f in self.food], "hazards":[]}, "you":next(s.to_dict() for s in self.snakes if s.id==current_snake_id)}

    def _check_collisions(self):
        dead = set(); living = [s for s in self.snakes if s.is_alive]
        for s in living:
            h = s.head
            if not (0 <= h.x < self.width and 0 <= h.y < self.height):
                if not self.silent: print(f"💀 Wall: {s.name}")
                dead.add(s.id); continue
            if h in list(s.body)[1:]:
                if not self.silent: print(f"💀 Self: {s.name}")
                dead.add(s.id); continue
            for o in living:
                if s.id == o.id: continue
                if h == o.head:
                    if not self.silent: print(f"💥 Head-to-Head: {s.name} vs {o.name}")
                    if len(s.body) <= len(o.body): dead.add(s.id)
                    if len(o.body) <= len(s.body): dead.add(o.id)
                elif h in set(o.body):
                    if not self.silent: print(f"💀 Body: {s.name} hit {o.name}")
                    dead.add(s.id); continue
        return dead

    def run_turn(self):
        self.turn += 1; living = [s for s in self.snakes if s.is_alive]
        if not living: return False # No one left, stop
        # print(f"\n--- Turn {self.turn} ---") # Console print less needed now

        moves = {}
        for s in living:
            gs = self._build_game_state(s.id)
            try:
                # Suppress move() prints
                with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
                    moves[s.id] = s.ai.move(gs)["move"]
            except Exception as e: print(f"⚠️ Move Error ({s.name}): {e}."); moves[s.id] = s.last_move_made

        ate = {}
        for s in living: s.body.insert(0, s.head.moved(moves[s.id])); s.last_move_made = moves[s.id]; s.health -= 1; ate[s.id] = False

        new_food = []
        for f in self.food:
            eaters = [s for s in living if s.head == f]
            if eaters:
                winner = max(eaters, key=lambda s: len(s.body))
                winner.health = 100; ate[winner.id] = True
            else: new_food.append(f)
        self.food = new_food

        for s in living:
            if not ate[s.id]: s.body.pop()

        dead = set();
        for s in living:
            if s.health <= 0:
                if not self.silent: print(f"💀 Starve: {s.name}")
                dead.add(s.id)
        dead.update(self._check_collisions())

        for s_id in dead:
            s = next((s for s in self.snakes if s.id == s_id), None)
            if s and s.is_alive:
                s.is_alive = False; gs = self._build_game_state(s.id)
                with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
                    try: s.ai.end(gs)
                    except Exception as e:
                        if not self.silent: print(f"Error calling end for {s.name}: {e}")
                if not self.silent: print(f"Eliminated: {s.name}")

        if random.random() < 0.15 or not self.food: self._spawn_food()
        if not self.silent: self.print_board()

        living_now = [s for s in self.snakes if s.is_alive]
        if len(living_now) == 1:
            if not self.silent: print(f"\n--- 🏆 GAME OVER --- {living_now[0].name} wins! --- 🏆")
            return False
        if not living_now:
            if not self.silent: print("\n--- 🏁 GAME OVER --- Draw! --- 🏁")
            return False
        return True

    def print_board(self):
        board = [['.' for _ in range(self.width)] for _ in range(self.height)]
        food_char = '🍎'

        for f in self.food: board[self.height - 1 - f.y][f.x] = food_char
        for i, snake in enumerate(self.snakes):
            if snake.is_alive:
                char = str(i % 10);
                head_char = chr(ord('A') + i)
                for j, p in enumerate(snake.body):
                    if 0 <= p.x < self.width and 0 <= p.y < self.height:
                        board[self.height - 1 - p.y][p.x] = f"\033[9{i + 1}m{head_char if j == 0 else char}\033[0m"

        cw = 3;
        sep = "-" * cw;
        print("+" + sep * self.width + "+")
        for y, row in enumerate(board):
            parts = [food_char + ' ' if c == food_char else ' . ' if c == '.' else ' ' + c + ' ' for c in row]
            print(f"|{''.join(parts)}| {self.height - 1 - y:<2}")
        print("+" + sep * self.width + "+")
        print(" " + ''.join([f"{x:^3}" for x in range(self.width)]) + " ")
        print("\n--- Status ---")
        for i, s in enumerate(self.snakes):
            if s.is_alive: print(
                f"\033[9{i + 1}m{chr(ord('A') + i)}{s.name[0]}\033[0m: {s.name} (L:{len(s.body)}, H:{s.health})")

def simulate(board_width, board_height, snake_ais, silent=False):
    if not silent:
        print(f"\n▶️ Starting simulation with {len(snake_ais)} snake(s)...")
    simulation = BattlesnakeSimulation(
        width=board_width,
        height=board_height,
        snake_ais=snake_ais,
        silent=silent
    )

    # Store original TURN_DELAY_SECONDS and set to 0 for silent mode
    global TURN_DELAY_SECONDS
    original_delay = TURN_DELAY_SECONDS
    if silent:
        TURN_DELAY_SECONDS = 0

    try:
        while simulation.run_turn():
            time.sleep(TURN_DELAY_SECONDS)

    except KeyboardInterrupt:
        if not silent:
            print("\nSimulation interrupted by user.")
    finally:
        if not silent:
            print("\nSimulation finished.")
        # Restore original delay
        TURN_DELAY_SECONDS = original_delay

        # Return the winner information
        living_snakes = [s for s in simulation.snakes if s.is_alive]
        if len(living_snakes) == 1:
            return living_snakes[0]  # Return the actual snake object
        else:
            return "Draw"

# --- Statistical Analysis Functions ---
def calculate_statistical_significance(wins_mutated, wins_baseline, total_games, confidence_level=0.95):
    """
    Calculate statistical significance of win rate difference using binomial test.
    Returns (is_significant, p_value, confidence_interval, effect_size)
    """
    if total_games == 0:
        return False, 1.0, (0, 0), 0

    # Calculate win rates (excluding draws for simplicity)
    total_decisive_games = wins_mutated + wins_baseline
    if total_decisive_games == 0:
        return False, 1.0, (0, 0), 0

    mutated_win_rate = wins_mutated / total_decisive_games

    # For binomial test, we test against null hypothesis of 50% win rate
    # Using normal approximation for large samples
    n = total_decisive_games
    p_null = 0.5  # Null hypothesis: no difference (50% win rate)

    # Calculate z-score
    if n * p_null * (1 - p_null) < 5:
        # Sample too small for normal approximation
        return False, 1.0, (0, 0), 0

    z_score = (mutated_win_rate - p_null) / math.sqrt(p_null * (1 - p_null) / n)

    # Calculate two-tailed p-value
    p_value = 2 * (1 - normal_cdf(abs(z_score)))

    # Calculate confidence interval for win rate
    alpha = 1 - confidence_level
    z_critical = inverse_normal_cdf(1 - alpha/2)
    margin_error = z_critical * math.sqrt(mutated_win_rate * (1 - mutated_win_rate) / n)
    ci_lower = max(0, mutated_win_rate - margin_error)
    ci_upper = min(1, mutated_win_rate + margin_error)

    # Effect size (Cohen's h for proportions)
    effect_size = 2 * (math.asin(math.sqrt(mutated_win_rate)) - math.asin(math.sqrt(p_null)))

    # Significance threshold
    alpha_threshold = 1 - confidence_level
    is_significant = p_value < alpha_threshold and mutated_win_rate > 0.5

    return is_significant, p_value, (ci_lower, ci_upper), effect_size

def normal_cdf(x):
    """Cumulative distribution function for standard normal distribution"""
    return 0.5 * (1 + math.erf(x / math.sqrt(2)))

def inverse_normal_cdf(p):
    """Inverse CDF for standard normal distribution (approximation)"""
    if p <= 0 or p >= 1:
        raise ValueError("p must be between 0 and 1")

    # Beasley-Springer-Moro algorithm approximation
    a = [0, -3.969683028665376e+01, 2.209460984245205e+02, -2.759285104469687e+02,
         1.383577518672690e+02, -3.066479806614716e+01, 2.506628277459239e+00]

    b = [0, -5.447609879822406e+01, 1.615858368580409e+02, -1.556989798598866e+02,
         6.680131188771972e+01, -1.328068155288572e+01]

    c = [0, -7.784894002430293e-03, -3.223964580411365e-01, -2.400758277161838e+00,
         -2.549732539343734e+00, 4.374664141464968e+00, 2.938163982698783e+00]

    d = [0, 7.784695709041462e-03, 3.224671290700398e-01, 2.445134137142996e+00,
         3.754408661907416e+00]

    if p < 0.5:
        # Use symmetry: if p < 0.5, compute for 1-p and negate
        return -inverse_normal_cdf(1 - p)

    if p == 0.5:
        return 0

    if p > 0.5:
        r = math.sqrt(-math.log(1 - p))
        if r <= 5:
            r = r - 1.6
            num = ((((a[6]*r + a[5])*r + a[4])*r + a[3])*r + a[2])*r + a[1]
            den = (((b[5]*r + b[4])*r + b[3])*r + b[2])*r + b[1]
            return num / den
        else:
            r = r - 5
            num = ((((c[6]*r + c[5])*r + c[4])*r + c[3])*r + c[2])*r + c[1]
            den = (((d[4]*r + d[3])*r + d[2])*r + d[1])
            return num / den

def calculate_required_sample_size(effect_size=0.2, power=0.8, alpha=0.05):
    """
    Calculate required sample size for detecting a given effect size.
    effect_size: Cohen's h (0.2 = small, 0.5 = medium, 0.8 = large)
    power: Statistical power (typically 0.8)
    alpha: Significance level (typically 0.05)
    """
    z_alpha = inverse_normal_cdf(1 - alpha/2)  # Two-tailed test
    z_beta = inverse_normal_cdf(power)

    # For proportion test
    n = 2 * ((z_alpha + z_beta) / effect_size) ** 2
    return math.ceil(n)

# --- Evolutionary Testing System ---
def mutate_heuristic(heuristic_values, mutation_rate=0.3, mutation_strength=0.2):
    """Create a mutated version of the heuristic values"""
    mutated = heuristic_values.copy()
    mutations_applied = 0

    for key in mutated:
        if random.random() < mutation_rate:
            # Apply random mutation
            current_value = mutated[key]
            mutation = random.uniform(-mutation_strength, mutation_strength)
            new_value = int(current_value * (1 + mutation))

            # Ensure the value actually changes
            if new_value != current_value:
                mutated[key] = new_value
                mutations_applied += 1

    # If no mutations were applied, force at least one
    if mutations_applied == 0:
        key = random.choice(list(mutated.keys()))
        current_value = mutated[key]
        mutation = random.uniform(-mutation_strength, mutation_strength)
        mutated[key] = int(current_value * (1 + mutation))

        # If still the same, add/subtract a small amount
        if mutated[key] == current_value:
            mutated[key] = current_value + random.choice([-1, 1])

    return mutated

def run_single_game(mutated_heuristic, baseline_heuristic, mutated_first, game_id):
    """Run a single game and return the result"""
    try:
        print(f"🎮 Starting game {game_id}...")

        if mutated_first:
            # Mutated first, baseline second
            # print(f"  Loading mutated AI for game {game_id}...")
            ai_1 = load_ai_from_file("wurmchen.py", heuristic_values=mutated_heuristic, silent=True)
            # print(f"  Loading baseline AI for game {game_id}...")
            ai_2 = load_ai_from_file("wurmchen.py", heuristic_values=baseline_heuristic, silent=True)
            mutated_is_first = True
        else:
            # Baseline first, mutated second
            # print(f"  Loading baseline AI for game {game_id}...")
            ai_1 = load_ai_from_file("wurmchen.py", heuristic_values=baseline_heuristic, silent=True)
            # print(f"  Loading mutated AI for game {game_id}...")
            ai_2 = load_ai_from_file("wurmchen.py", heuristic_values=mutated_heuristic, silent=True)
            mutated_is_first = False

        if not ai_1 or not ai_2:
            print(f"❌ Failed to load AIs for game {game_id}")
            return None

        print(f"  Running simulation for game {game_id}...")
        winner = simulate(BOARD_WIDTH, BOARD_HEIGHT, [ai_1, ai_2], silent=True)
        # print(f"  Game {game_id} completed, winner: {winner}")

        # Determine the result
        if winner == "Draw":
            return {'game_id': game_id, 'result': 'draw'}
        elif hasattr(winner, 'ai') and hasattr(winner.ai, 'heuristikValues'):
            # Check if the winner has mutated heuristic
            if winner.ai.heuristikValues == mutated_heuristic:
                return {'game_id': game_id, 'result': 'mutated_win'}
            else:
                return {'game_id': game_id, 'result': 'baseline_win'}
        else:
            return {'game_id': game_id, 'result': 'draw'}

    except Exception as e:
        print(f"❌ Error in game {game_id}: {e}")
        return None

def test_heuristic_vs_baseline_sequential(mutated_heuristic, baseline_heuristic, num_games):
    """Test a mutated heuristic against a baseline heuristic sequentially (fallback method)"""
    print(f"Testing mutated heuristic vs baseline over {num_games} games (sequential)...")

    wins_mutated = 0
    wins_baseline = 0
    draws = 0
    completed_games = 0

    # Run games alternating starting positions
    for game in range(num_games):
        mutated_first = (game % 2 == 0)  # Alternate starting positions
        result = run_single_game(mutated_heuristic, baseline_heuristic, mutated_first, game)

        if result is not None:
            if result['result'] == 'mutated_win':
                wins_mutated += 1
            elif result['result'] == 'baseline_win':
                wins_baseline += 1
            else:
                draws += 1
            completed_games += 1

            # Progress indicator
            if num_games <= 10 or completed_games % max(1, num_games // 4) == 0:
                print(f"  Completed {completed_games}/{num_games} games")

    if completed_games == 0:
        return None

    win_rate_mutated = wins_mutated / completed_games
    print(f"Results: Mutated {wins_mutated}, Baseline {wins_baseline}, Draws {draws}")
    print(f"Mutated win rate: {win_rate_mutated:.2%}")

    # Calculate statistical significance
    is_significant, p_value, confidence_interval, effect_size = calculate_statistical_significance(
        wins_mutated, wins_baseline, completed_games
    )

    print(f"Statistical Analysis:")
    print(f"  P-value: {p_value:.4f}")
    print(f"  95% Confidence Interval: [{confidence_interval[0]:.3f}, {confidence_interval[1]:.3f}]")
    print(f"  Effect Size (Cohen's h): {effect_size:.3f}")
    print(f"  Statistically Significant: {'✅ YES' if is_significant else '❌ NO'}")

    return {
        'win_rate': win_rate_mutated,
        'wins_mutated': wins_mutated,
        'wins_baseline': wins_baseline,
        'draws': draws,
        'is_significant': is_significant,
        'p_value': p_value,
        'confidence_interval': confidence_interval,
        'effect_size': effect_size
    }

def test_heuristic_vs_baseline_simple_threads(mutated_heuristic, baseline_heuristic, num_games, max_workers):
    """Test using simple threading like the main simulation"""
    if max_workers is None:
        max_workers = min(4, num_games)  # Very conservative

    print(f"Testing mutated heuristic vs baseline over {num_games} games using {max_workers} simple threads...")
    start_time = time.time()

    # Results storage with thread safety
    results = []
    results_lock = threading.Lock()

    def run_game_simple(game_id):
        """Simple thread function"""
        try:
            mutated_first = (game_id % 2 == 0)
            result = run_single_game(mutated_heuristic, baseline_heuristic, mutated_first, game_id)

            with results_lock:
                results.append(result)
                completed = len([r for r in results if r is not None])
                if completed % 5 == 0 or completed == num_games:
                    elapsed = time.time() - start_time
                    print(f"  Completed {completed}/{num_games} games ({elapsed:.1f}s total)")

        except Exception as e:
            print(f"❌ Error in simple thread {game_id}: {e}")
            with results_lock:
                results.append(None)

    # Run games in small batches to avoid overwhelming the system
    batch_size = min(max_workers, 4)
    for batch_start in range(0, num_games, batch_size):
        batch_end = min(batch_start + batch_size, num_games)
        threads = []

        # Start batch of threads
        for game_id in range(batch_start, batch_end):
            thread = threading.Thread(target=run_game_simple, args=(game_id,))
            threads.append(thread)
            thread.start()

        # Wait for batch to complete
        for thread in threads:
            thread.join(timeout=60)  # 60 second timeout per thread
            if thread.is_alive():
                print(f"❌ Thread timeout in batch {batch_start}-{batch_end}")

    # Process results
    wins_mutated = 0
    wins_baseline = 0
    draws = 0
    completed_games = 0

    for result in results:
        if result is not None:
            if result['result'] == 'mutated_win':
                wins_mutated += 1
            elif result['result'] == 'baseline_win':
                wins_baseline += 1
            else:
                draws += 1
            completed_games += 1

    total_time = time.time() - start_time
    if completed_games == 0:
        return None

    win_rate_mutated = wins_mutated / completed_games
    print(f"Results: Mutated {wins_mutated}, Baseline {wins_baseline}, Draws {draws}")
    print(f"Mutated win rate: {win_rate_mutated:.2%} (Total time: {total_time:.1f}s)")

    # Calculate statistical significance
    is_significant, p_value, confidence_interval, effect_size = calculate_statistical_significance(
        wins_mutated, wins_baseline, completed_games
    )

    print(f"Statistical Analysis:")
    print(f"  P-value: {p_value:.4f}")
    print(f"  95% Confidence Interval: [{confidence_interval[0]:.3f}, {confidence_interval[1]:.3f}]")
    print(f"  Effect Size (Cohen's h): {effect_size:.3f}")
    print(f"  Statistically Significant: {'✅ YES' if is_significant else '❌ NO'}")

    return {
        'win_rate': win_rate_mutated,
        'wins_mutated': wins_mutated,
        'wins_baseline': wins_baseline,
        'draws': draws,
        'is_significant': is_significant,
        'p_value': p_value,
        'confidence_interval': confidence_interval,
        'effect_size': effect_size
    }

def test_heuristic_vs_baseline(mutated_heuristic, baseline_heuristic, num_games, max_workers, use_threading):
    """Test a mutated heuristic against a baseline heuristic for num_games"""

    # For small numbers of games or if threading is disabled, use sequential
    if not use_threading or num_games <= 4:
        return test_heuristic_vs_baseline_sequential(mutated_heuristic, baseline_heuristic, num_games)

    try:
        return test_heuristic_vs_baseline_simple_threads(mutated_heuristic, baseline_heuristic, num_games, max_workers)
    except Exception as e:
        print(f"❌ Threading failed: {e}")
        print("Falling back to sequential execution...")
        return test_heuristic_vs_baseline_sequential(mutated_heuristic, baseline_heuristic, num_games)

def evolutionary_heuristic_optimization(generations=5, num_games_per_test=None, max_workers=None, use_threading=True,
                                      min_effect_size=0.3, confidence_level=0.95, power=0.8):
    """
    Run evolutionary optimization on heuristic values with statistical significance testing.

    Args:
        generations: Number of generations to run
        num_games_per_test: Number of games per test (auto-calculated if None)
        max_workers: Number of parallel workers
        use_threading: Whether to use threading
        min_effect_size: Minimum effect size to detect (Cohen's h)
        confidence_level: Statistical confidence level (default 95%)
        power: Statistical power (default 80%)
    """
    # Get default heuristic values from wurmchen.py
    default_ai = load_ai_from_file("wurmchen.py")
    if not default_ai:
        print("❌ Failed to load wurmchen.py")
        return

    current_best = default_ai.heuristikValues.copy()
    current_best_score = 0.5  # Baseline score (50% win rate against itself)

    # Calculate required sample size for statistical significance
    if num_games_per_test is None:
        alpha = 1 - confidence_level
        required_sample_size = calculate_required_sample_size(min_effect_size, power, alpha)
        # Add some buffer for draws and round to nice number
        num_games_per_test = max(100, int(required_sample_size * 1.2))
        # Round to nearest 50 for cleaner numbers
        num_games_per_test = ((num_games_per_test + 24) // 50) * 50

    if max_workers is None:
        max_workers = min(multiprocessing.cpu_count(), 8, num_games_per_test)

    print("🧬 Starting Evolutionary Heuristic Optimization with Statistical Significance Testing")
    print("=" * 80)
    print(f"Statistical Parameters:")
    print(f"  Minimum Effect Size (Cohen's h): {min_effect_size}")
    print(f"  Confidence Level: {confidence_level:.1%}")
    print(f"  Statistical Power: {power:.1%}")
    print(f"  Required Sample Size: {num_games_per_test} games per test")
    print(f"  Significance Threshold: p < {1-confidence_level:.3f}")
    print()
    if use_threading:
        print(f"Using {max_workers} threads for parallel game execution")
    else:
        print("Using sequential execution")
    print("Default heuristic values:")
    for key, value in current_best.items():
        print(f"  {key}: {value}")
    print("=" * 80)

    for generation in range(generations):
        print(f"\n🧬 Generation {generation + 1}/{generations}")

        # Create mutated version
        mutated_heuristic = mutate_heuristic(current_best)

        print("Mutated heuristic values:")
        mutations_found = False
        for key, value in mutated_heuristic.items():
            if value != current_best[key]:
                print(f"  {key}: {current_best[key]} → {value}")
                mutations_found = True

        if not mutations_found:
            print("  No mutations in this generation")
            continue  # Skip testing if no mutations occurred

        # Test the mutated heuristic
        results = test_heuristic_vs_baseline(mutated_heuristic, current_best, num_games_per_test, max_workers, use_threading)

        if results is None:
            print("❌ Testing failed, skipping generation")
            continue

        # Check if this is a statistically significant improvement
        if results['is_significant'] and results['win_rate'] > current_best_score:
            print(f"🎉 STATISTICALLY SIGNIFICANT IMPROVEMENT FOUND!")
            print(f"   Win rate: {results['win_rate']:.2%} (was {current_best_score:.2%})")
            print(f"   P-value: {results['p_value']:.4f}")
            print(f"   Effect size: {results['effect_size']:.3f}")
            current_best = mutated_heuristic.copy()
            current_best_score = results['win_rate']

            print("New best heuristic values:")
            for key, value in current_best.items():
                print(f"  {key}: {value}")
        elif results['win_rate'] > current_best_score:
            print(f"⚠️  Improvement found but NOT statistically significant")
            print(f"   Win rate: {results['win_rate']:.2%} (was {current_best_score:.2%})")
            print(f"   P-value: {results['p_value']:.4f} (not significant)")
            print(f"   Rejecting improvement due to insufficient statistical evidence")
        else:
            print(f"❌ No improvement. Win rate: {results['win_rate']:.2%} (current best: {current_best_score:.2%})")
            if not results['is_significant']:
                print(f"   P-value: {results['p_value']:.4f} (not significant)")

    print("\n" + "=" * 80)
    print("🏆 FINAL RESULTS")
    print("=" * 80)
    print(f"Best win rate achieved: {current_best_score:.2%}")
    print(f"Statistical significance required: p < {1-confidence_level:.3f}")
    print(f"Minimum effect size detected: {min_effect_size}")
    print(f"Games per test: {num_games_per_test}")
    print()
    print("Best heuristic values:")
    for key, value in current_best.items():
        print(f"  {key}: {value}")
    print("=" * 80)

    return current_best

# --- Main Execution ---
if __name__ == "__main__":
    print("Setting up Battlesnake Simulation...")

    # Check if user wants to run evolutionary optimization
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--evolve":
        evolutionary_heuristic_optimization()
    else:
        # Load AI modules
        print("\nLoading AI Modules...")
        snake_modules_loaded = []
        for item in SNAKE_FILES_TO_LOAD:
            ai_module = load_ai_from_file(item)
            if ai_module:
                snake_modules_loaded.append(ai_module)

        if len(snake_modules_loaded) < 1:
            print("\n❌ Error: No AIs loaded. Cannot start simulation.")
        else:
            thread = threading.Thread(target=simulate, args=(BOARD_WIDTH, BOARD_HEIGHT, snake_modules_loaded))
            thread.start()
